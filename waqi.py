import requests
import json

# Your WAQI API token
API_TOKEN = 'be6d7811a0f4263a36cc56351fcc11a73d3b76cf'

# Coordinates (example: New York City)
latitude = 40.7128
longitude = -74.0060

# API endpoint
url = f'https://api.waqi.info/feed/geo:{latitude};{longitude}/?token={API_TOKEN}'

# Make the request
response = requests.get(url)

# Check if response is successful
if response.status_code == 200:
    data = response.json()

    # Remove the 'forecast' field if it exists
    if 'data' in data and 'forecast' in data['data']:
        del data['data']['forecast']

    # Save full response (without forecast) to a JSON file
    with open('waqi_response_no_forecast.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

    print("✅ Data saved to waqi_response_no_forecast.json (forecast removed)")

else:
    print("❌ Failed to retrieve data:", response.status_code)
